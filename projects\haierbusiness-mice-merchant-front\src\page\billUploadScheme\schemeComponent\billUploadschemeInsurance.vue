<script setup lang="ts">
// 方案互动-保险产品
import { message } from 'ant-design-vue';
import { onMounted, ref, reactive, watch, nextTick, defineProps, defineEmits } from 'vue';

import { errorModal, resolveParam, routerParam, formatNumberThousands } from '@haierbusiness-front/utils';

const props = defineProps({
  schemeItem: {
    type: Object,
    default: {},
  },
  schemeCacheItem: {
    type: Object,
    default: {},
  },
  isSchemeCache: {
    type: Boolean,
    default: false,
  },
  schemeIndex: {
    type: Number,
    default: 0,
  },
  schemeType: {
    // 方案提报类型 // 查看需求-view / 未提报-notReported / 已提报-reported / 查看方案-schemeView / 待竞价 - notBidding / 竞价完成 - biddingView / 账单上传 - billUpload
    type: String,
    default: '',
  },
});

const emit = defineEmits(['schemePriceEmit', 'schemeInsurancesEmit']);

const oldSchemeList = ref<any[]>([]);
const newSchemeList = ref<any[]>([]);

const subtotal = ref<number>(0); // 小计

// 价格计算
const priceCalcFun = () => {
  subtotal.value = 0;

  newSchemeList.value.forEach((e) => {
    // 根据模式选择计算方式
    if (props.schemeType === 'billUpload') {
      if (e.billUnitPrice && e.billPersonNum) {
        subtotal.value += e.billUnitPrice * e.billPersonNum;
      }
    } else {
      if (e.schemeUnitPrice && e.schemePersonNum) {
        subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
      }
    }
  });

  emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

// 人数变化时重新计算价格
const changePersonNum = (index: number) => {
  priceCalcFun();

  // 触发暂存
  insuranceTempSave();
};

// 账单人数变化时重新计算价格
const changeBillPersonNum = (index: number) => {
  // 账单模式下使用billPersonNum和billUnitPrice计算
  calculateBillPrice();

  // 触发暂存
  insuranceTempSave();
};

// 账单单价变化时重新计算价格
const changeBillUnitPrice = (index: number) => {
  // 账单模式下使用billPersonNum和billUnitPrice计算
  calculateBillPrice();

  // 触发暂存
  insuranceTempSave();
};

// 计算账单价格
const calculateBillPrice = () => {
  subtotal.value = 0;
  newSchemeList.value.forEach((e) => {
    if (props.schemeType === 'billUpload') {
      if (e.billUnitPrice && e.billPersonNum) {
        subtotal.value += e.billUnitPrice * e.billPersonNum;
      }
    } else {
      if (e.schemeUnitPrice && e.schemePersonNum) {
        subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
      }
    }
  });

  emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
};

watch(
  () => props.schemeItem,
  (newObj) => {
    console.log('%c [ 保险产品数据 ]-调试', 'font-size:13px; background:pink; color:#bf2c9f;', newObj);
    console.log('%c [ 保险产品列表 ]-调试', 'font-size:13px; background:yellow; color:#000;', newObj?.insurances);

    oldSchemeList.value = JSON.parse(JSON.stringify(newObj))?.insurances || [];

    if (props.isSchemeCache && props.schemeCacheItem) {
      // 缓存 - 反显
      newSchemeList.value = props.schemeCacheItem?.insurances || [];

      // 价格计算
      priceCalcFun();
    } else {
      // 直接复制左边的数据到右边，保持所有原始字段
      const demandData = JSON.parse(JSON.stringify(newObj))?.insurances || [];
      newSchemeList.value = demandData.map((e: any) => {
        console.log('%c [ 处理单个保险项目 ]-调试', 'font-size:12px; background:lightblue; color:#000;', e);

        // 直接复制所有字段，确保右边显示和左边一致
        const rightSideData = {
          // 保持原有字段
          ...e,

          // 确保必要的ID字段
          miceDemandInsuranceId: e.id,
          miceSchemeInsuranceId: e.miceSchemeInsuranceId || e.id,

          // 确保人数字段存在（用于编辑）
          schemePersonNum: e.schemePersonNum || e.personNum,

          // 确保单价字段存在
          schemeUnitPrice: e.schemeUnitPrice || e.demandUnitPrice,

          // 确保其他必要字段
          sourceId: e.sourceId || null,
        };

        console.log('%c [ 右侧数据处理结果 ]-调试', 'font-size:12px; background:lightgreen; color:#000;', rightSideData);

        // 如果是账单上传模式，添加账单相关字段
        if (props.schemeType === 'billUpload') {
          return {
            ...rightSideData,
            // 账单字段初始化 - 直接使用左边的值
            billPersonNum: e.billPersonNum || e.schemePersonNum || e.personNum,
            billUnitPrice: e.billUnitPrice !== undefined ? e.billUnitPrice : (e.schemeUnitPrice || e.demandUnitPrice || null),
            invoiceTempId: e.invoiceTempId || null,
            statementTempId: e.statementTempId || null,
          };
        }

        return rightSideData;
      });

      console.log('%c [ 最终右侧数据列表 ]-调试', 'font-size:13px; background:orange; color:#fff;', newSchemeList.value);
    }

    // 小计 - 根据模式选择计算方式
    subtotal.value = 0;
    newSchemeList.value.forEach((e) => {
      if (props.schemeType === 'billUpload') {
        if (e.billUnitPrice && e.billPersonNum) {
          subtotal.value += e.billUnitPrice * e.billPersonNum;
        }
      } else {
        if (e.schemeUnitPrice && e.schemePersonNum) {
          subtotal.value += e.schemeUnitPrice * e.schemePersonNum;
        }
      }
    });

    emit('schemePriceEmit', { type: 'insurance', totalPrice: subtotal.value, schemeIndex: props.schemeIndex });
  },
  {
    immediate: true,
    deep: true,
  },
);

const schemePlanLabelList = ['参保人数', '保险产品', '单价'];

const changePrice = (index: number) => {
  if (newSchemeList.value[index].biddingPrice) {
    newSchemeList.value[index].planPrice =
      newSchemeList.value[index].biddingPrice * newSchemeList.value[index].schemePersonNum;
  }

  // 价格计算
  priceCalcFun();
};

// 方案数据转换为账单数据
const convertSchemeToAccountData = (schemeData: any) => {
  return {
    // 直接对应字段
    sourceId: schemeData.sourceId || null,
    miceDemandInsuranceId: schemeData.miceDemandInsuranceId,
    miceSchemeInsuranceId: schemeData.miceSchemeInsuranceId || schemeData.miceDemandInsuranceId,
    demandDate: schemeData.demandDate,
    demandUnitPrice: schemeData.demandUnitPrice,
    productId: schemeData.productId,
    productMerchantId: schemeData.productMerchantId,
    insuranceName: schemeData.insuranceName,
    insuranceContent: schemeData.insuranceContent,
    description: schemeData.description,

    // 转换字段：方案 → 账单
    billPersonNum: schemeData.billPersonNum || schemeData.schemePersonNum || 0,
    billUnitPrice: schemeData.billUnitPrice !== undefined ? schemeData.billUnitPrice : (schemeData.schemeUnitPrice || null),

    // 新增账单字段（临时ID，需要从发票和水单上传功能获取）
    invoiceTempId: schemeData.invoiceTempId || null,
    statementTempId: schemeData.statementTempId || null,

    // 保单附件相关字段
    insuranceAttachments: schemeData.insuranceAttachments || [],

    // 保留原有方案字段（用于显示对比）
    schemePersonNum: schemeData.schemePersonNum,
    schemeUnitPrice: schemeData.schemeUnitPrice,
  };
};

// 更新保险项目的发票临时ID
const updateInsuranceInvoiceId = (insuranceIndex: number, invoiceTempId: string | null) => {
  if (newSchemeList.value[insuranceIndex]) {
    newSchemeList.value[insuranceIndex].invoiceTempId = invoiceTempId;
    insuranceTempSave();
  }
};

// 更新保险项目的水单临时ID
const updateInsuranceStatementId = (insuranceIndex: number, statementTempId: string | null) => {
  if (newSchemeList.value[insuranceIndex]) {
    newSchemeList.value[insuranceIndex].statementTempId = statementTempId;
    insuranceTempSave();
  }
};

// 更新保险项目的附件临时ID和路径
const updateInsuranceAttachmentId = (insuranceIndex: number, attachmentData: any) => {
  console.log('保险组件接收到的附件数据:', attachmentData);
  console.log('当前保险列表:', newSchemeList.value);
  console.log('要更新的索引:', insuranceIndex);

  if (newSchemeList.value[insuranceIndex] && attachmentData) {
    console.log('更新前的保险项目:', newSchemeList.value[insuranceIndex]);

    newSchemeList.value[insuranceIndex].attachmentTempId = attachmentData.tempId;
    // 使用拼接好的完整URL数组
    newSchemeList.value[insuranceIndex].insuranceAttachments = attachmentData.insuranceAttachments || [];

    console.log('更新后的保险项目:', newSchemeList.value[insuranceIndex]);
    console.log('保险组件更新附件数据:', {
      index: insuranceIndex,
      tempId: attachmentData.tempId,
      attachments: attachmentData.insuranceAttachments
    });

    insuranceTempSave();
  } else {
    console.log('更新失败 - 条件不满足:', {
      hasInsuranceItem: !!newSchemeList.value[insuranceIndex],
      hasAttachmentData: !!attachmentData,
      insuranceIndex,
      listLength: newSchemeList.value.length
    });
  }
};

// 获取提交数据（过滤掉UI相关字段）
const getSubmitData = () => {
  return newSchemeList.value.map(item => {
    if (props.schemeType === 'billUpload') {
      return convertSchemeToAccountData(item);
    }
    return item;
  });
};

// 暂存
const insuranceTempSave = () => {
  console.log('%c [ 保险暂存 - newSchemeList ]-调试', 'font-size:13px; background:red; color:#fff;', newSchemeList.value);

  // 根据schemeType决定数据格式
  const processedData = props.schemeType === 'billUpload'
    ? newSchemeList.value.map(item => convertSchemeToAccountData(item))
    : [...newSchemeList.value];

  console.log('%c [ 保险暂存 - processedData ]-调试', 'font-size:13px; background:purple; color:#fff;', processedData);

  emit('schemeInsurancesEmit', {
    schemeInsurances: processedData,
    schemeIndex: props.schemeIndex,
  });
};
// 校验
const insuranceSub = () => {
  let isVerPassed = true;

  // newSchemeList.value.forEach((e, i) => {
  //   if (!e.schemeUnitPrice) {
  //     message.error('请输入' + e.demandDate + '保险' + (i + 1) + '竞价单价');

  //     isVerPassed = false;
  //     return;
  //   }
  // });

  if (isVerPassed) {
    insuranceTempSave();
  }

  return isVerPassed;
};

defineExpose({
  insuranceSub,
  insuranceTempSave,
  updateInsuranceInvoiceId,
  updateInsuranceStatementId,
  updateInsuranceAttachmentId,
  getSubmitData
});

onMounted(async () => {});
</script>

<template>
  <!-- 保险产品 -->
  <div class="scheme_vehicle">
    <div class="common_table">
      <!-- 左侧 -->
      <div class="common_table_l" v-if="props.schemeType !== 'notBidding' && props.schemeType !== 'biddingView'">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>保险需求</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in oldSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '保险产品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value pl12 pr12">
              {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.insuranceName || '-' }}
                </template>
                {{ item.insuranceName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
                </template>
                {{ item.demandUnitPrice ? item.demandUnitPrice + '元/人' : '-' }}
              </a-tooltip>
            </div>
          </div>
        </div>
      </div>

      <div class="common_table_divide"></div>

      <!-- 右侧 -->
      <div class="common_table_r">
        <div class="scheme_plan_title">
          <div class="scheme_plan_img mr10"></div>
          <span>{{ props.schemeType === 'billUpload' ? '账单信息' : '保险方案' }}</span>
        </div>

        <div class="scheme_plan_table mt20" v-for="(item, idx) in newSchemeList" :key="idx">
          <div class="scheme_plan_index">
            {{ '保险产品' + (idx + 1) }}
          </div>
          <div class="scheme_plan_list1">
            <div class="scheme_plan_label" v-for="(label, index) in schemePlanLabelList" :key="index">
              {{ label }}
            </div>
          </div>
          <div class="scheme_plan_list2">
            <div class="scheme_plan_value p0">
              <!-- 只读模式 -->
              <div
                class="pl12"
                v-if="
                  props.schemeType === 'notBidding' ||
                  props.schemeType === 'biddingView' ||
                  props.schemeType === 'schemeView'
                "
              >
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                </a-tooltip>
              </div>
              <!-- 账单上传模式 - 编辑账单人数 -->
              <div class="pl12" v-else-if="props.schemeType === 'billUpload'">
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.billPersonNum">
                    {{ item.billPersonNum ? item.billPersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.billPersonNum"
                    @change="changeBillPersonNum(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="账单人数"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
              <!-- 其他编辑模式 - 编辑方案人数 -->
              <div class="pl12" v-else>
                <a-tooltip placement="topLeft">
                  <template #title v-if="item.schemePersonNum">
                    {{ item.schemePersonNum ? item.schemePersonNum + '人' : '-' }}
                  </template>
                  <a-input-number
                    v-model:value="item.schemePersonNum"
                    @change="changePersonNum(idx)"
                    style="width: calc(100% - 30px)"
                    placeholder="人数"
                    :min="1"
                    :max="999"
                    :precision="0"
                    :bordered="false"
                    :controls="false"
                    allow-clear
                  />
                  <div class="scheme_plan_edit"></div>
                </a-tooltip>
              </div>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <a-tooltip placement="topLeft">
                <template #title>
                  {{ item.insuranceName || '-' }}
                </template>
                {{ item.insuranceName || '-' }}
              </a-tooltip>
            </div>
            <div class="scheme_plan_value pl12 pr12">
              <!-- 账单上传模式显示账单单价（只读） -->
              <div v-if="props.schemeType === 'billUpload'">
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ (item.billUnitPrice !== null && item.billUnitPrice !== undefined) ? item.billUnitPrice + '元/人' : (item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-') }}
                  </template>
                  {{ (item.billUnitPrice !== null && item.billUnitPrice !== undefined) ? item.billUnitPrice + '元/人' : (item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-') }}
                </a-tooltip>
              </div>
              <!-- 其他模式显示方案单价 -->
              <div v-else>
                <a-tooltip placement="topLeft">
                  <template #title>
                    {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                  </template>
                  {{ item.schemeUnitPrice ? item.schemeUnitPrice + '元/人' : '-' }}
                </a-tooltip>
              </div>
            </div>
          </div>
          <div class="scheme_plan_list3 pr12">
            <div class="scheme_plan_price">
              <div class="scheme_plan_price_label">总额：</div>
              <div class="scheme_plan_price_value">
                <!-- 账单上传模式使用方案单价和账单人数计算 -->
                <template v-if="props.schemeType === 'billUpload'">
                  {{
                    item.schemeUnitPrice && item.billPersonNum
                      ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.billPersonNum)
                      : (item.schemeUnitPrice && item.schemePersonNum
                          ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                          : '-')
                  }}
                </template>
                <!-- 其他模式使用方案数据计算 -->
                <template v-else>
                  {{
                    item.schemeUnitPrice && item.schemePersonNum
                      ? '¥' + formatNumberThousands(item.schemeUnitPrice * item.schemePersonNum)
                      : '-'
                  }}
                </template>
              </div>
            </div>
            <div class="scheme_plan_price_tip mt4">
              <!-- 账单上传模式显示账单计算公式 -->
              <div v-if="props.schemeType === 'billUpload' && item.billUnitPrice && item.billPersonNum">
                {{ item.billPersonNum + '人*' + item.billUnitPrice + '(账单单价)' }}
              </div>
              <!-- 其他模式显示方案计算公式 -->
              <div v-else-if="props.schemeType !== 'billUpload' && item.schemeUnitPrice && item.schemePersonNum">
                {{ item.schemePersonNum + '人*' + item.schemeUnitPrice + '(单价)' }}
              </div>
            </div>
          </div>
        </div>

        <div v-show="subtotal" class="scheme_plan_subtotal mt16">
          {{ '小计：¥' + formatNumberThousands(subtotal) }}
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
.scheme_vehicle {
  .scheme_plan_img {
    background: url('@/assets/image/demand/demand_insurance.png');
    background-repeat: no-repeat;
    background-size: 18px 18px;
    background-position: left center;
  }

  // 人数输入框样式，模仿活动组件
  .scheme_plan_value {
    :deep(.ant-input-number) {
      border: none;
      box-shadow: none;

      .ant-input-number-input {
        height: auto;
        padding: 0;
        text-align: left;
        width: 100%;
        font-weight: normal;
        font-size: 14px;
        color: #333;
        border-bottom: none;
        border: none;

        &::placeholder {
          color: #bfbfbf;
        }
      }

      &:hover .ant-input-number-input,
      &:focus .ant-input-number-input,
      &.ant-input-number-focused .ant-input-number-input {
        border: none;
        box-shadow: none;
      }
    }

    .scheme_plan_edit {
      margin-left: 5px;
      display: inline-flex;
      vertical-align: middle;

      width: 16px;
      height: 18px;
      background: url('@/assets/image/common/edit_gray.png');
      background-repeat: no-repeat;
      background-size: 16px 16px;
    }
  }

  :deep(.ant-input-number .ant-input-number-input) {
    height: 24px;
    padding: 0 5px;
    text-align: end;

    width: 84px;
    font-weight: 500;
    font-size: 14px;
    color: #1868db;
    text-align: right;
    border-bottom: 1px solid #4e5969;
  }

  .p0 {
    padding: 0 !important;
  }
}
</style>
